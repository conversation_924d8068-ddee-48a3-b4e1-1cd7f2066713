<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title>Dashboard | BooknGo</title>
        <meta name="description" content="Your BooknGo dashboard">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

        <!-- Styles / Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    </head>
    <body class="bg-gray-50 font-sans antialiased">
        <!-- Modern Navbar -->
        <?php echo $__env->make('components.modern-navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Welcome Section -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">
                    Welcome back, <?php echo e(Auth::user()->name); ?>!
                </h1>
                <p class="mt-2 text-gray-600">
                    <?php if(Auth::user()->isAdmin()): ?>
                        Manage the entire BooknGo platform from your admin dashboard.
                    <?php elseif(Auth::user()->isOperator()): ?>
                        Manage your buses, trips, and bookings from your operator dashboard.
                    <?php else: ?>
                        Find and book bus tickets for your next journey.
                    <?php endif; ?>
                </p>
            </div>

            <?php if(Auth::user()->isAdmin()): ?>
                <?php echo $__env->make('dashboard.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php elseif(Auth::user()->isOperator()): ?>
                <?php echo $__env->make('dashboard.operator', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php else: ?>
                <?php echo $__env->make('dashboard.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
        </div>


    </body>
</html>
<?php /**PATH C:\xampp\htdocs\BooknGo\resources\views/dashboard.blade.php ENDPATH**/ ?>